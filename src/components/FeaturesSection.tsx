'use client';

import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import gradientGL from 'gradient-gl';
import Button from './ui/Button';

const FeaturesSection = () => {
  const t = useTranslations('hero');

  useEffect(() => {
    // Initialize gradient-gl for features section
    gradientGL('b5.fc04', '#features-gradient-bg');
  }, []);

  // Intersection observers for staggered animations
  const { ref: backgroundRef, inView: backgroundInView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: servicesRef, inView: servicesInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: benefitsRef, inView: benefitsInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section ref={backgroundRef} className="py-32 relative overflow-hidden">
      {/* WebGL Gradient Background */}
      <div
        id="features-gradient-bg"
        className={`gradient-container absolute inset-0 z-0 transition-opacity duration-1500 ease-out delay-1500 ${
          backgroundInView ? 'opacity-100' : 'opacity-0'
        }`}
      ></div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        {/* Title Section - Left side only */}
        <div className="flex flex-col md:flex-row mb-16">
          <div className="w-full">
            <div
              ref={titleRef}
              className={`transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-7xl font-normal text-albatros-ivory">
                Djelatnosti OD Albatros proširuju se shodno Vašim administrativnim potrebama.
              </h2>
            </div>
          </div>
          {/* Right 1/3 - Empty space */}
          <div className="md:w-1/3">
            {/* Intentionally left empty */}
          </div>
        </div>

        {/* Services Section - Right side only */}
        <div className="flex flex-col md:flex-row mb-8">
          {/* Left 1/3 - Empty space */}
          <div className="md:w-1/3">
            <p className="text-sm text-albatros-ivory font-light">
              <span className="font-bold">Naše usluge</span><br />
                i djelatnosti
            </p>
          </div>
          {/* Right 2/3 - Services List */}
          <div className="md:w-2/3">
            <div
              ref={servicesRef}
              className={`flex flex-col text-xl text-albatros-ivory border-t border-white/20 transition-all duration-1000 ease-out delay-500 ${
                servicesInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <span className="py-3 border-b border-white/20">Pribavljanje i dostava ličnih dokumenata</span>
              <span className="py-3 border-b border-white/20">Pribavljanje i dostava izvoda iz matičnih knjiga</span>
              <span className="py-3 border-b border-white/20">Pribavljanje i dostava potvrda i uvjerenja</span>
              <span className="py-3 border-b border-white/20">Ovjera kod nadležnih institucija (općina, notar, sud)</span>
              <span className="py-3 border-b border-white/20">Pribavljanje i dostava zemljišnoknjižnih izvadaka</span>
              <span className="py-3 border-b border-white/20">Administrativna podrška pravnim licima</span>
              <span className="py-3 border-b border-white/20">Organizacija prijevoda kod sudskog tumača</span>
            </div>
          </div>
        </div>

        {/* Napomena Section - Right side only */}
        <div className="flex flex-col md:flex-row">
          {/* Left 1/3 - Empty space */}
          <div className="md:w-1/3">
            {/* Intentionally left empty */}
          </div>
          {/* Right 2/3 - Important Notes */}
          <div className="md:w-2/3">
            <div
              ref={benefitsRef}
              className={`transition-all duration-1000 ease-out delay-700 ${
                benefitsInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h3 className="text-lg text-albatros-ivory mb-4 leading-tight">Napomena:</h3>
              <div className="flex flex-col mb-8 font-semibold text-sm text-albatros-ivory/90">
                <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Vremenski rok pribavljanja dokumenata zavisi od vrste dokumenata i vremena potrebnog institucijama.</span>
                <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Za obavljanje naših djelatnosti potrebna nam je Vaša ovjerena punomoć.</span>
                <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Građani BiH u BiH punomoć vade i ovjeravaju u općini.</span>
                <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Državljani Bosne i Hercegovine u inostranstvu punomoć mogu ovjeriti u konzulatu.</span>
              </div>
              <Button variant="outline" size="lg">
                {t('contactButton')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
