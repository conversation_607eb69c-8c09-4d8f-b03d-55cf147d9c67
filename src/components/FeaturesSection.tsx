'use client';

import { useEffect, useRef, useState } from 'react';
import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import gradientGL from 'gradient-gl';
import Button from './ui/Button';

// Custom hook for auto-staggering children animations
const useStaggeredChildren = (baseDelay = 0, staggerDelay = 200) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  const { ref: inViewRef, inView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  useEffect(() => {
    if (inView) {
      setIsVisible(true);

      // Apply staggered animations to children
      if (containerRef.current) {
        const children = Array.from(containerRef.current.children) as HTMLElement[];
        children.forEach((child, index) => {
          const delay = baseDelay + (index * staggerDelay);
          child.style.transitionDelay = `${delay}ms`;
          child.style.transitionDuration = '200ms';
          child.style.transitionTimingFunction = 'ease-out';
          child.style.transitionProperty = 'opacity, transform';

          if (isVisible) {
            child.style.opacity = '1';
            child.style.transform = 'translateY(0)';
          }
        });
      }
    }
  }, [inView, isVisible, baseDelay, staggerDelay]);

  // Combine refs
  const setRefs = (element: HTMLDivElement | null) => {
    containerRef.current = element;
    inViewRef(element);
  };

  return {
    ref: setRefs,
    isVisible,
    containerRef
  };
};

const FeaturesSection = () => {
  const t = useTranslations('hero');

  useEffect(() => {
    // Initialize gradient-gl for features section
    gradientGL('b5.fc04', '#features-gradient-bg');
  }, []);

  // Intersection observers for staggered animations
  const { ref: backgroundRef, inView: backgroundInView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  // Use staggered animation hooks for services and napomena items
  const servicesStagger = useStaggeredChildren(500, 100);
  const napomenaStagger = useStaggeredChildren(700, 150);

  const { ref: buttonRef, inView: buttonInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section ref={backgroundRef} className="py-32 relative overflow-hidden">
      {/* WebGL Gradient Background */}
      <div
        id="features-gradient-bg"
        className={`gradient-container absolute inset-0 z-0 transition-opacity duration-1500 ease-out delay-1500 ${
          backgroundInView ? 'opacity-100' : 'opacity-0'
        }`}
      ></div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        {/* Title Section - Left side only */}
        <div className="flex flex-col md:flex-row mb-16">
          <div className="w-full">
            <div
              ref={titleRef}
              className={`transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-7xl font-normal text-albatros-ivory">
                Djelatnosti OD Albatros proširuju se shodno Vašim administrativnim potrebama.
              </h2>
            </div>
          </div>
          {/* Right 1/3 - Empty space */}
          <div className="md:w-1/3">
            {/* Intentionally left empty */}
          </div>
        </div>

        {/* Services Section - Right side only */}
        <div className="flex flex-col md:flex-row mb-8">
          {/* Left 1/3 - Empty space */}
          <div className="md:w-1/3">
            <p className="text-sm text-albatros-ivory font-light">
              <span className="font-bold">Albatros</span><br />
                usluge i djelatnosti
            </p>
          </div>
          {/* Right 2/3 - Services List */}
          <div className="md:w-2/3">
            <div
              ref={servicesStagger.ref}
              className="flex flex-col text-xl text-albatros-ivory border-t border-white/20"
            >
              <span className="py-3 border-b border-white/20 opacity-0 translate-y-8">Pribavljanje i dostava ličnih dokumenata</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-y-8">Pribavljanje i dostava izvoda iz matičnih knjiga</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-y-8">Pribavljanje i dostava potvrda i uvjerenja</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-y-8">Ovjera kod nadležnih institucija (općina, notar, sud)</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-y-8">Pribavljanje i dostava zemljišnoknjižnih izvadaka</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-y-8">Administrativna podrška pravnim licima</span>
              <span className="py-3 border-b border-white/20 opacity-0 translate-y-8">Organizacija prijevoda kod sudskog tumača</span>
            </div>
          </div>
        </div>

        {/* Napomena Section - Right side only */}
        <div className="flex flex-col md:flex-row">
          {/* Left 1/3 - Empty space */}
          <div className="md:w-1/3">
            {/* Intentionally left empty */}
          </div>
          {/* Right 2/3 - Important Notes */}
          <div className="md:w-2/3">
            <div
              ref={napomenaStagger.ref}
              className="space-y-4"
            >
              <h3 className="text-lg text-albatros-ivory mb-4 leading-tight opacity-0 translate-y-8">Napomena:</h3>
              <div className="flex flex-col mb-8 font-semibold text-sm text-albatros-ivory/90 space-y-2">
                <span className="before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5 opacity-0 translate-y-8">Vremenski rok pribavljanja dokumenata zavisi od vrste dokumenata i vremena potrebnog institucijama.</span>
                <span className="before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5 opacity-0 translate-y-8">Za obavljanje naših djelatnosti potrebna nam je Vaša ovjerena punomoć.</span>
                <span className="before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5 opacity-0 translate-y-8">Građani BiH u BiH punomoć vade i ovjeravaju u općini.</span>
                <span className="before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5 opacity-0 translate-y-8">Državljani Bosne i Hercegovine u inostranstvu punomoć mogu ovjeriti u konzulatu.</span>
              </div>
              <div className="opacity-0 translate-y-8">
                <Button variant="outlinewhite" size="lg">
                  {t('contactButton')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
