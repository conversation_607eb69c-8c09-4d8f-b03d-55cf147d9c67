import { useEffect, useRef, useState } from 'react';
import { useInView } from 'react-intersection-observer';

/**
 * Custom hook for auto-staggering children animations
 * 
 * This hook automatically applies staggered animations to all direct children
 * of a container element when it comes into view. Each child will animate
 * with a progressive delay, creating a smooth staggered effect.
 * 
 * @param baseDelay - Initial delay before the first animation starts (in ms)
 * @param staggerDelay - Delay between each child animation (in ms)
 * @param threshold - Intersection observer threshold (0-1)
 * @param duration - Animation duration (in ms)
 * @returns Object with ref, isVisible state, and containerRef
 * 
 * @example
 * ```tsx
 * const stagger = useStaggeredAnimation(500, 100);
 * 
 * return (
 *   <div ref={stagger.ref} className="container">
 *     <div className="opacity-0 translate-y-8">Item 1</div>
 *     <div className="opacity-0 translate-y-8">Item 2</div>
 *     <div className="opacity-0 translate-y-8">Item 3</div>
 *   </div>
 * );
 * ```
 */
export const useStaggeredAnimation = (
  baseDelay = 0, 
  staggerDelay = 200, 
  threshold = 0.3,
  duration = 200
) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  
  const { ref: inViewRef, inView } = useInView({
    threshold,
    triggerOnce: true,
  });
  
  useEffect(() => {
    if (inView) {
      setIsVisible(true);
      
      // Apply staggered animations to children
      if (containerRef.current) {
        const children = Array.from(containerRef.current.children) as HTMLElement[];
        children.forEach((child, index) => {
          const delay = baseDelay + (index * staggerDelay);
          child.style.transitionDelay = `${delay}ms`;
          child.style.transitionDuration = `${duration}ms`;
          child.style.transitionTimingFunction = 'ease-out';
          child.style.transitionProperty = 'opacity, transform';
          
          if (isVisible) {
            child.style.opacity = '1';
            child.style.transform = 'translateY(0)';
          }
        });
      }
    }
  }, [inView, isVisible, baseDelay, staggerDelay, duration]);
  
  // Combine refs
  const setRefs = (element: HTMLDivElement | null) => {
    containerRef.current = element;
    inViewRef(element);
  };
  
  return {
    ref: setRefs,
    isVisible,
    containerRef
  };
};

export default useStaggeredAnimation;
